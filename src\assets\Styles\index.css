@font-face {
  font-family: "Evalter";
  src: url(../fonts/fonnts.com-Evalter.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-1";
  src: url(../fonts/MounstaDemo-KVMoX.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-2";
  src: url(../fonts/MounstaDemo-MA20P.woff2) format("woff2");
}
@font-face {
  font-family: "Almendra";
  src: url(../../../assets/fonts/Almendra-Regular.woff2) format("woff2");
}
/*========== Colors ==========*/
/*========== Font and Typography ==========*/
/*========== px value in rem ==========*/
/*========== Font weight ==========*/
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#root,
main {
  width: 100%;
  height: 100%;
}

html {
  font-size: clamp(0.875rem, 1vw + 0.5rem, 1.125rem);
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  padding: 1vmax;
}

body::-webkit-scrollbar {
  background-color: transparent;
  width: 0.5rem;
}

body::-webkit-scrollbar-thumb {
  border-radius: 0.25rem;
  background-color: rgb(181, 181, 181);
}

body {
  scroll-behavior: smooth;
}/*# sourceMappingURL=index.css.map */