import React from 'react'
import skillsArray from '../../../utils/SkillsArray/skillsArray'
import './hero.css'

const Hero = () => {
    return (
        <>
            <section className="hero">
                <div className="hero-container">
                    <h1>What I Know</h1>
                    {/* <div className="box-wrapper">
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                        <div className="box"></div>
                    </div> */}

                    <div class="parent">
                        {skillsArray.map((item, index) => {
                            return (
                                <div class="box"  key={index}>
                                    <img className={item.id} width={200} height={200} src={item.icon} alt={item.name} />
                                </div>
                            )
                        })}
                    </div>

                </div>
            </section>
        </>
    )
}

export default Hero
