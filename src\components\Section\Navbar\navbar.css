@font-face {
  font-family: "Evalter";
  src: url(../fonts/fonnts.com-Evalter.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-1";
  src: url(../fonts/MounstaDemo-KVMoX.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-2";
  src: url(../fonts/MounstaDemo-MA20P.woff2) format("woff2");
}
@font-face {
  font-family: "Almendra";
  src: url(../../../assets/fonts/Almendra-Regular.woff2) format("woff2");
}
/*========== Colors ==========*/
/*========== Font and Typography ==========*/
/*========== px value in rem ==========*/
/*========== Font weight ==========*/
/*========== Navbar ==========*/
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.875rem;
}
.navbar .logo h4 {
  font-family: "Mounsta-1";
  font-size: 1.125rem;
  color: #000;
  line-height: 0.9;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.navbar .logo .small {
  font-family: "Mounsta-1";
  font-size: 0.875rem;
  color: #1a1a1a;
  padding-left: 0.0625rem;
  letter-spacing: 0.0625rem;
}
.navbar button {
  background-color: #4463ee;
  color: #fff;
  letter-spacing: 1px;
  border: none;
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: "Mounsta-2";
  transition: background-color 0.3s ease;
  border-radius: 0.25rem;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.navbar button:hover {
  background-color: #3557ec;
}

@media screen and (min-width: 768px) {
  .navbar {
    padding: 1rem;
  }
  .navbar .logo h4 {
    font-size: 1.25rem;
    letter-spacing: 0.0625rem;
  }
  .navbar .logo .small {
    font-size: 1rem;
    letter-spacing: 0.125rem;
  }
  .navbar button {
    font-size: 0.875rem;
    padding: 0.75rem 1.25rem;
    letter-spacing: 0.125rem;
    font-family: "Mounsta-2";
    font-weight: 100;
  }
}/*# sourceMappingURL=navbar.css.map */