import React from 'react';
import styled from 'styled-components';

const Switch = () => {
    return (
        <StyledWrapper>
            <div className="toggle-switch">
                <label className="switch-label">
                    <input type="checkbox" className="checkbox" />
                    <span className="slider" />
                </label>
            </div>
        </StyledWrapper>
    );
}

const StyledWrapper = styled.div`
  .toggle-switch {
    position: relative;
    width: 5.25rem;       /* 100px */
    height: 2.125rem;     /* 50px */
    --light: #d8dbe0;
    --dark: #28292c;
    --link: rgb(27, 129, 112);
    --link-hover: rgb(24, 94, 82);
  }

  .switch-label {
    position: absolute;
    width: 100%;
    height: 2.5rem;     /* 50px */
    background-color: var(--dark);
    border-radius: 1.5625rem; /* 25px */
    cursor: pointer;
    border: 0.1875rem solid var(--dark); /* 3px */
  }

  .checkbox {
    position: absolute;
    display: none;
  }

  .slider {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 1.5625rem; /* 25px */
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .checkbox:checked ~ .slider {
    background-color: var(--light);
  }

  .slider::before {
    content: "";
    position: absolute;
    top: 0.25rem;        /* 10px */
    left: 0.125rem;       /* 10px */
    width: 1.5rem;     /* 25px */
    height: 1.5rem;    /* 25px */
    border-radius: 50%;
    -webkit-box-shadow: inset 0.75rem -0.25rem 0rem 0rem var(--light); /* 12px -4px 0px 0px */
    box-shadow: inset 0.75rem -0.25rem 0rem 0rem var(--light);
    background-color: var(--dark);
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .checkbox:checked ~ .slider::before {
    -webkit-transform: translateX(3.125rem); /* 50px */
    -ms-transform: translateX(3.125rem);
    transform: translateX(3.125rem);
    background-color: var(--dark);
    -webkit-box-shadow: none;
    box-shadow: none;
  }
`;

export default Switch;
