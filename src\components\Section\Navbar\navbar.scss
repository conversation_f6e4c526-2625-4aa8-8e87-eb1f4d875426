@import "../Partials-scss/boilerplate";

/*========== Navbar ==========*/
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $px-14;
    // padding: 1vmax;
    // background-color: red;
    .logo {
        h4 {
            font-family: "Mounsta-1";
            font-size: $px-18;
            color: $primary-color;
            line-height: 0.9;
            user-select: none;
        }

        .small {
            font-family: "Mounsta-1";
            font-size: $px-14;
            color: $secondary-color;
            padding-left: $px-1;
            letter-spacing: $px-1;
        }
    }

    button {
        background-color: $button-color;
        color: $button-hover-text-color;
        letter-spacing: 1px;
        border: none;
        padding: $px-10 $px-16;
        font-size: $px-14;
        font-weight: 600;
        font-family: "Mounsta-2";
        transition: background-color 0.3s ease;
        border-radius: $px-4;
        cursor: pointer;
        user-select: none;
        // --webkit-text-stroke: 0.5px #fff;
        // --webkit-text-fill-color: $button-hover-text-color;
        &:hover {
            background-color: $button-hover-color;
        }
    }
}

@media screen and (min-width: 768px) {
    .navbar {
        padding: $px-16;
        .logo h4 {
            font-size: $px-20;
            letter-spacing: $px-1;
        }
        .logo .small {
            font-size: $px-16;
            letter-spacing: $px-2;
        }
        button {
            font-size: $px-14;
            padding: $px-12 $px-20;
            letter-spacing: $px-2;
            font-family: "Mounsta-2";
            font-weight: 100;
        }
    }
}
