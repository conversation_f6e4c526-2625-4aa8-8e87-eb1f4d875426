@font-face {
  font-family: "Evalter";
  src: url(../fonts/fonnts.com-Evalter.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-1";
  src: url(../fonts/MounstaDemo-KVMoX.woff2) format("woff2");
}
@font-face {
  font-family: "Mounsta-2";
  src: url(../fonts/MounstaDemo-MA20P.woff2) format("woff2");
}
@font-face {
  font-family: "Almendra";
  src: url(../../../assets/fonts/Almendra-Regular.woff2) format("woff2");
}
/*========== Colors ==========*/
/*========== Font and Typography ==========*/
/*========== px value in rem ==========*/
/*========== Font weight ==========*/
/*========== Hero ==========*/
.hero {
  width: 100%;
}
.hero .hero-container {
  min-height: 85vh;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: column;
}
.hero .hero-container h1 {
  font-size: 3.8vmax;
  font-family: "Mounsta-1";
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.hero .hero-container .parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 0.875rem;
  border: 0.125rem solid #000;
  padding: 0.625rem;
}
.hero .hero-container .parent .box {
  min-width: 12vmax;
  min-height: 12vmax;
  background-color: red;
  display: inline-block;
}
.hero .hero-container .parent .box img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  -o-object-fit: contain;
     object-fit: contain;
  image-rendering: auto;
  aspect-ratio: 1/1;
}

@media (min-width: 320px) and (max-width: 455px) {
  .hero .hero-container {
    min-height: 85vh;
  }
  .hero .hero-container .parent {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 0.875rem;
    border: 0.125rem solid #000;
    padding: 0.625rem;
  }
}
@media (min-width: 455px) and (max-width: 536px) {
  .hero .hero-container {
    min-height: 85vh;
  }
  .hero .hero-container .parent {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.875rem;
    border: 0.125rem solid #000;
    padding: 0.625rem;
  }
}
@media (min-width: 536px) and (max-width: 844px) {
  .hero .hero-container {
    min-height: 80vh;
  }
  .hero .hero-container h1 {
    font-size: 5.5vmax;
    font-family: "Mounsta-1";
    text-align: center;
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
  }
  .hero .hero-container .parent {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.875rem;
    border: 0.125rem solid #000;
    padding: 0.625rem;
  }
}
@media (min-width: 844px) and (max-width: 1440px) {
  .hero .hero-container {
    min-height: 90vh;
    padding: 1.875rem 0;
  }
  .hero .hero-container h1 {
    font-size: 4vmax;
    font-family: "Mounsta-1";
    text-align: center;
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
    padding-bottom: 1.875rem;
  }
  .hero .hero-container .parent {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.875rem;
    border: 0.125rem solid #000;
    padding: 0.625rem;
  }
  .hero .hero-container .box {
    min-width: 10vmax;
    min-height: 10vmax;
    background-color: red;
    display: inline-block;
  }
}/*# sourceMappingURL=hero.css.map */