@import "../Partials-scss/boilerplate";

/*========== Hero ==========*/
.hero {
    width: 100%;

    .hero-container {
        min-height: 85vh;
        // padding: $px-30 0;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-direction: column;

        h1 {
            font-size: 3.8vmax;
            font-family: "Mounsta-1";
            text-align: center;
            user-select: none;
        }

        .parent {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(5, 1fr);
            gap: $px-14;
            border: $px-2 solid $primary-color;
            padding: $px-10;

            .box {
                min-width: 12vmax;
                min-height: 12vmax;
                background-color: red;
                display: inline-block;  

                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                    object-fit: contain;
                    image-rendering: auto;
                    aspect-ratio: 1/1;
                }
            }
        }

    }
}

@media (min-width: 320px) and (max-width: 455px) {
    .hero{
        .hero-container{
            min-height: 85vh;

            .parent {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(5, 1fr);
                gap: $px-14;
                border: $px-2 solid $primary-color;
                padding: $px-10;
    
            }
        }
    }
}

@media (min-width: 455px) and (max-width: 536px) {
    .hero{
        .hero-container{
            min-height: 85vh;

            .parent {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: repeat(3, 1fr);
                gap: $px-14;
                border: $px-2 solid $primary-color;
                padding: $px-10;
    
            }
        }
    }
}
@media (min-width: 536px) and (max-width: 844px) {
    .hero{
        .hero-container{
            min-height: 80vh;
            h1 {
            font-size: 5.5vmax;
            font-family: "Mounsta-1";
            text-align: center;
            user-select: none;
        }
            .parent {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                grid-template-rows: repeat(3, 1fr);
                gap: $px-14;
                border: $px-2 solid $primary-color;
                padding: $px-10;
    
            }
        }
    }
}
@media (min-width: 844px) and (max-width: 1440px) {
    .hero{
        .hero-container{
            min-height: 90vh;
            padding: $px-30 0;
            h1 {
            font-size: 4vmax;
            font-family: "Mounsta-1";
            text-align: center;
            user-select: none;
            padding-bottom: $px-30 ;
        }
            .parent {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                grid-template-rows: repeat(3, 1fr);
                gap: $px-14;
                border: $px-2 solid $primary-color;
                padding: $px-10;
    
            }
            .box {
                min-width: 10vmax;
                min-height: 10vmax;
                background-color: red;
                display: inline-block;
            }
        }
    }
}
